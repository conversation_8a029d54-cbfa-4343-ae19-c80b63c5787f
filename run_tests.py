#!/usr/bin/env python3
"""
Test runner for ATM project.
Runs both QML tests and Python unit tests.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_qml_tests():
    """Run QML tests using the Qt test framework."""
    print("=" * 60)
    print("Running QML Tests")
    print("=" * 60)
    
    try:
        result = subprocess.run([
            sys.executable, "App/qml_tests.py"
        ], capture_output=True, text=True, cwd=Path.cwd())
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"Error running QML tests: {e}")
        return False


def run_python_tests():
    """Run Python unit tests using pytest."""
    print("\n" + "=" * 60)
    print("Running Python Unit Tests")
    print("=" * 60)
    
    try:
        # Install test requirements if needed
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "test_requirements.txt"
        ], check=True, capture_output=True)
        
        # Run pytest with coverage
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "test_server.py", 
            "test_websocket_client.py",
            "-v",
            "--tb=short",
            "--cov=server",
            "--cov=websocket_client",
            "--cov-report=term-missing"
        ], cwd=Path.cwd())
        
        return result.returncode == 0
    except Exception as e:
        print(f"Error running Python tests: {e}")
        return False


def main():
    """Run all tests and report results."""
    print("ATM Project Test Suite")
    print("=" * 60)
    
    qml_success = run_qml_tests()
    python_success = run_python_tests()
    
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    print(f"QML Tests: {'✅ PASSED' if qml_success else '❌ FAILED'}")
    print(f"Python Tests: {'✅ PASSED' if python_success else '❌ FAILED'}")
    
    if qml_success and python_success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
