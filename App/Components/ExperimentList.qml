// qmllint disable unqualified

import QtQuick
import QtQuick.Controls.Basic
import QtQuick.Layouts

Rectangle {
    id: container
    width: parent.width * 0.4
    height: parent.height
    color: "#393939"

    signal selectExperiment(var experimentData)

    ExperimentListFilterMenu {
        id: experimentListFilterMenu
    }

    function openFilterMenu() {
        experimentListFilterMenu.open();
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 15
        spacing: 10

        RowLayout {
            CustomSearchBar {
                id: experimentSearchBar
                Layout.fillWidth: true
                Layout.preferredHeight: implicitHeight
                placeholderTextString: "Search by parameter (e.g. ionSrc=ECR1)"
                borderRadius: 5

                onSearchTriggered: function(searchText) {
                    experimentListModel.clear();

                    if (searchText !== "") {
                        var resultSortingParam = paramsDropdown.getSortingQuery();
                        server.get_experiment_data(searchText + "&" + resultSortingParam);
                    } else {
                        paramsDropdown.fetchSortedExperiments();
                    }
                }

                onTextCleared: {
                    paramsDropdown.fetchSortedExperiments();
                }
            }

            Button {
                id: showExperimentFiltersBtn
                Layout.preferredHeight: 40
                Layout.minimumWidth: contentItem.implicitWidth + 20
                text: qsTr("Show Filters")

                contentItem: Row {
                    spacing: 8
                    anchors.centerIn: parent

                    Text {
                        text: "☰"  // Menu icon as text
                        color: "white"
                        font.pixelSize: 16
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    Text {
                        text: showExperimentFiltersBtn.text
                        color: "white"
                        font: showExperimentFiltersBtn.font
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }

                background: Rectangle {
                    color: "#343434"
                    radius: 4
                    border.color: showExperimentFiltersBtn.hovered ? "#48aaad" : "transparent"
                    border.width: 1
                }

                onClicked: {
                    openFilterMenu();
                }
            }
        }

        CustomComboBox {
            id: paramsDropdown
            Layout.fillWidth: true
            Layout.preferredHeight: 40
            options: [
                qsTr("Start Date (Most Recent)"),
                qsTr("End Date (Most Recent)"),
                qsTr("Atomic Mass Ascending"),
                qsTr("Atomic Mass Descending"),
                qsTr("Atomic Number Ascending"),
                qsTr("Atomic Number Descending"),
                qsTr("Ion Source"),
                qsTr("Target"),
                qsTr("q1"),
                qsTr("q2"),
                qsTr("q3"),
                qsTr("Max Energy Ascending"),
                qsTr("Max Energy Descending"),
                qsTr("Charge-to-Mass Ratio Ascending"),
                qsTr("Charge-to-Mass Ratio Descending"),
                qsTr("Rigidity Ascending"),
                qsTr("Rigidity Descending")
            ]

            function getSortingQuery() {
                var sortingMap = {
                    "Atomic Mass Ascending": "orderBy=a",
                    "Atomic Mass Descending": "orderBy=a&desc=true",
                    "Atomic Number Ascending": "orderBy=z",
                    "Atomic Number Descending": "orderBy=z&desc=true",
                    "Ion Source": "orderBy=ionSrc",
                    "Target": "orderBy=target",
                    "q1": "orderBy=q1",
                    "q2": "orderBy=q2",
                    "q3": "orderBy=q3",
                    "End Date (Most Recent)": "orderBy=endDate&desc=true",
                    "Start Date (Most Recent)": "orderBy=startDate&desc=true",
                    "Max Energy Ascending": "orderBy=maxEnergy",
                    "Max Energy Descending": "orderBy=maxEnergy&desc=true",
                    "Charge-to-Mass Ratio Ascending": "orderBy=chargeMassRatio",
                    "Charge-to-Mass Ratio Descending": "orderBy=chargeMassRatio&desc=true",
                    "Rigidity Ascending": "orderBy=rigidity",
                    "Rigidity Descending": "orderBy=rigidity&desc=true"
                };

                return sortingMap[currentValue] || "";
            }

            function fetchSortedExperiments() {
                experimentListModel.clear();

                var sortingQuery = getSortingQuery();
                server.get_experiment_data(sortingQuery);
            }

            onActivated: {
                fetchSortedExperiments();
            }
        }

        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            background: Rectangle {
                color: "#393939"
            }
            ScrollBar.vertical.policy: ScrollBar.AlwaysOn

            Component.onCompleted: server.get_experiment_data("orderBy=startDate&desc=true")

            contentItem: ListView {
                id: experimentListView
                clip: true
                boundsBehavior: Flickable.StopAtBounds
                model: experimentListModel

                property int selectedIndex: -1

                delegate: ItemDelegate {
                    id: experimentListItem
                    width: ListView.view.width

                    required property string expNum
                    required property real startDate
                    required property real endDate
                    required property var atomicMass
                    required property var atomicNumber
                    required property string ionSrc
                    required property string target
                    required property real q1
                    required property real q2
                    required property real q3
                    required property real maxEnergy
                    required property real chargeMassRatio
                    required property real rigidity

                    required property int index

                    text: {
                        var selection = paramsDropdown.displayText;
                        var infoToShow = qsTr("Exp #") + expNum;

                        var selectionMap = {
                            "Start Date (Most Recent)": "startDate",
                            "End Date (Most Recent)": "endDate",
                            "Atomic Mass Ascending": "atomicMass",
                            "Atomic Mass Descending": "atomicMass",
                            "Atomic Number Ascending": "atomicNumber",
                            "Atomic Number Descending": "atomicNumber",
                            "Ion Source": "ionSrc",
                            "Target": "target",
                            "q1": "q1",
                            "q2": "q2",
                            "q3": "q3",
                            "Max Energy Ascending": "maxEnergy",
                            "Max Energy Descending": "maxEnergy",
                            "Charge-to-Mass Ratio Ascending": "chargeMassRatio",
                            "Charge-to-Mass Ratio Descending": "chargeMassRatio",
                            "Rigidity Ascending": "rigidity",
                            "Rigidity Descending": "rigidity"
                        };

                        var selectedProperty = selectionMap[selection];
                        if (selectedProperty) {
                            if (selectedProperty === "startDate" || selectedProperty === "endDate") {
                                infoToShow += " | " + selectedProperty + ": " + Qt.formatDateTime(new Date(this[selectedProperty] * 1000), "MM/dd/yyyy");
                            } else {
                                infoToShow += " | " + selectedProperty + ": " + this[selectedProperty];
                            }
                        }

                        return infoToShow;
                    }

                    onClicked: {
                        experimentListView.selectedIndex = index;
                        container.selectExperiment({
                            expNum: expNum,
                            startDate: startDate,
                            endDate: endDate,
                            atomicMass: atomicMass,
                            atomicNumber: atomicNumber,
                            ionSrc: ionSrc,
                            target: target,
                            q1: q1,
                            q2: q2,
                            q3: q3,
                            maxEnergy: maxEnergy,
                            chargeMassRatio: chargeMassRatio,
                            rigidity: rigidity
                        });
                    }

                    contentItem: Text {
                        text: experimentListItem.text
                        color: "white"
                        font.pixelSize: 14
                        elide: Text.ElideRight
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignLeft
                        leftPadding: 16
                        rightPadding: 16
                    }

                    background: Rectangle {
                        color: experimentListView.selectedIndex === experimentListItem.index ? "#48aaad" : "#393939"
                    }
                }

                Text {
                    id: noResultsText
                    x: parent.width / 2 - width / 2
                    y: parent.height / 2 - height / 2
                    text: qsTr("No results")
                    color: "white"
                    font.pixelSize: 16
                    visible: experimentListModel.count == 0
                }
            }
        }
    }
}
