// qmllint disable unqualified

import QtQuick
import QtQuick.Layouts
import QtQuick.Controls.Basic
import QtQuick.Controls as Controls

Popup {
    id: popup
    width: parent.width
    height: parent.height
    modal: true
    focus: true
    closePolicy: Popup.NoAutoClose

    background: Rectangle {
        color: "#393939"
    }

    function getFilterQuery() {
        var queryParts = [];

        // Date range filter
        if (calendarGrid.selectedStartDate && calendarGrid.selectedEndDate) {
            var startDate = calendarGrid.selectedStartDate;
            var endDate = calendarGrid.selectedEndDate;

            // Convert dates to Unix epoch time
            var startDateUnix = Math.floor(startDate.getTime() / 1000);
            var endDateUnix = Math.floor(endDate.getTime() / 1000);

            queryParts.push("startDate=[" + startDateUnix + ":" + endDateUnix + "]");
        }

        // Range slider filters
        if (q2Slider.firstValue > q2Slider.fromValue || q2Slider.secondValue < q2Slider.toValue) {
            queryParts.push("q2=" + q2Slider.firstValue + ":");
            queryParts.push(q2Slider.secondValue);
        }

        if (q3Slider.firstValue > q3Slider.fromValue || q3Slider.secondValue < q3Slider.toValue) {
            queryParts.push("q3=" + q3Slider.firstValue + ":");
            queryParts.push(q3Slider.secondValue);
        }

        if (maxEnergySlider.firstValue > maxEnergySlider.fromValue || maxEnergySlider.secondValue < maxEnergySlider.toValue) {
            queryParts.push("maxEnergy=" + maxEnergySlider.firstValue + ":");
            queryParts.push(maxEnergySlider.secondValue);
        }

        if (chargeMassRatioSlider.firstValue > chargeMassRatioSlider.fromValue || chargeMassRatioSlider.secondValue < chargeMassRatioSlider.toValue) {
            queryParts.push("chargeMassRatio=" + chargeMassRatioSlider.firstValue + ":");
            queryParts.push(chargeMassRatioSlider.secondValue);
        }

        if (rigiditySlider.firstValue > rigiditySlider.fromValue || rigiditySlider.secondValue < rigiditySlider.toValue) {
            queryParts.push("rigidity=" + rigiditySlider.firstValue + ":");
            queryParts.push(rigiditySlider.secondValue);
        }

        // Ion source filter
        if (ionSourceFilter.currentText !== qsTr("All Ion Sources")) {
            queryParts.push("ionSrc=" + ionSourceFilter.currentText);
        }

        // Target filter
        if (targetFilter.currentText !== qsTr("All Targets")) {
            queryParts.push("target=" + targetFilter.currentText);
        }

        return queryParts.join("&");
        
    }

    Button {
        id: closeButton
        text: qsTr("✕")
        font.pixelSize: 16
        width: 40
        height: 40
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 15
        onClicked: {
            popup.close();
        }

        background: Rectangle {
            color: "#4c4c4c"
            radius: 4
        }

        contentItem: Text {
            text: closeButton.text
            color: "white"
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            anchors.fill: parent
        }

        ToolTip.text: qsTr("Close")
    }

    ScrollView {
        anchors.fill: parent
        anchors.margins: 15
        anchors.topMargin: 70  // Space for close button
        contentWidth: availableWidth

        ColumnLayout {
            width: parent.width
            spacing: 10

            Text {
                text: qsTr("Experiment Filters")
                color: "white"
                font.pixelSize: 16
                font.bold: true
            }

            Text {
                text: qsTr("Date Range Selection")
                color: "white"
                font.pixelSize: 14
                font.bold: true
            }

            RowLayout {
                Layout.fillWidth: true
                spacing: 10

                CustomSearchBar {
                    id: dateSearchField
                    Layout.fillWidth: true
                    Layout.preferredHeight: 35
                    placeholderTextString: "Search date (MM/YYYY or MM/DD/YYYY)"
                    placeholderColor: "#999999"

                    onSearchTriggered: function(searchText) {
                        handleDateSearch(searchText);
                    }

                    onTextChanged: {
                        if (text.length === 0) return;
                        handleDateSearch(text);
                    }

                    function handleDateSearch(input) {
                        var searchDate = parseDateInput(input);
                        if (searchDate) {
                            calendarGrid.month = searchDate.getMonth();
                            calendarGrid.year = searchDate.getFullYear();
                            calendarGrid.selectedStartDate = searchDate;
                            calendarGrid.selectedEndDate = searchDate;
                        }
                    }

                    function parseDateInput(input) {
                        var cleanInput = input.trim();
                        var currentYear = new Date().getFullYear();

                        // MM/YYYY format
                        var monthYearMatch = cleanInput.match(/^(\d{1,2})\/(\d{4})$/);
                        if (monthYearMatch) {
                            var month = parseInt(monthYearMatch[1]) - 1; // 0-based month
                            var year = parseInt(monthYearMatch[2]);
                            if (month >= 0 && month <= 11 && year >= 1997 && year <= currentYear) {
                                return new Date(year, month, 1);
                            }
                        }

                        // MM/DD/YYYY format
                        var fullDateMatch = cleanInput.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
                        if (fullDateMatch) {
                            var month = parseInt(fullDateMatch[1]) - 1; // 0-based month
                            var day = parseInt(fullDateMatch[2]);
                            var year = parseInt(fullDateMatch[3]);
                            if (month >= 0 && month <= 11 && day >= 1 && day <= 31 && year >= 1997 && year <= currentYear) {
                                return new Date(year, month, day);
                            }
                        }

                        return null;
                    }
                }

                Button {
                    id: todayButton
                    text: qsTr("Today")
                    Layout.preferredHeight: 35
                    Layout.minimumWidth: 60

                    background: Rectangle {
                        color: todayButton.pressed ? "#48aaad" : "#4c4c4c"
                        radius: 4
                        border.color: todayButton.hovered ? "#48aaad" : "transparent"
                        border.width: 1
                    }

                    contentItem: Text {
                        text: todayButton.text
                        color: "white"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }

                    onClicked: {
                        var today = new Date();
                        calendarGrid.month = today.getMonth();
                        calendarGrid.year = today.getFullYear();
                        calendarGrid.selectedStartDate = today;
                        calendarGrid.selectedEndDate = today;
                        dateSearchField.text = "";
                    }
                }
            }

            RowLayout {
                Layout.fillWidth: true
                spacing: 10

                Button {
                    id: prevButton
                    text: qsTr("◀")
                    enabled: !(calendarGrid.year === 1997 && calendarGrid.month === 0)
                    onClicked: {
                        if (calendarGrid.month === 0) {
                            calendarGrid.month = 11;
                            calendarGrid.year--;
                        } else {
                            calendarGrid.month--;
                        }
                    }

                    background: Rectangle {
                        color: prevButton.enabled ? (prevButton.down ? "#5a5a5a" : "#4c4c4c") : "#2a2a2a"
                        radius: 4
                    }

                    contentItem: Text {
                        text: prevButton.text
                        color: prevButton.enabled ? "white" : "#666666"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }

                Text {
                    Layout.fillWidth: true
                    text: Qt.locale().monthName(calendarGrid.month) + " " + calendarGrid.year
                    color: "white"
                    font.pixelSize: 14
                    horizontalAlignment: Text.AlignHCenter
                }

                Button {
                    id: nextButton
                    text: qsTr("▶")
                    enabled: !(calendarGrid.year === new Date().getFullYear() && calendarGrid.month === new Date().getMonth())
                    onClicked: {
                        if (calendarGrid.month === 11) {
                            calendarGrid.month = 0;
                            calendarGrid.year++;
                        } else {
                            calendarGrid.month++;
                        }
                    }

                    background: Rectangle {
                        color: nextButton.enabled ? (nextButton.down ? "#5a5a5a" : "#4c4c4c") : "#2a2a2a"
                        radius: 4
                    }

                    contentItem: Text {
                        text: nextButton.text
                        color: nextButton.enabled ? "white" : "#666666"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }

            Controls.MonthGrid {
                id: calendarGrid
                Layout.fillWidth: true
                Layout.preferredHeight: 200
                month: new Date().getMonth()
                year: new Date().getFullYear()
                locale: Qt.locale()

                property date selectedStartDate: new Date()
                property date selectedEndDate: new Date()
                property bool selectingStartDate: true

                delegate: Rectangle {
                    id: dayDelegate
                    required property date date
                    required property int day
                    required property int month
                    required property bool today

                    width: 30
                    height: 30
                    color: {
                        if (dayDelegate.today)
                            return "#48aaad";
                        if (isInSelectedRange())
                            return "#6bb6b9";
                        if (isSelectedDate())
                            return "#48aaad";
                        return "transparent";
                    }
                    border.color: dayDelegate.today ? "#ffffff" : "transparent"
                    border.width: 1
                    radius: 4

                    function isSelectedDate() {
                        return (dayDelegate.date.getTime() === calendarGrid.selectedStartDate.getTime() || dayDelegate.date.getTime() === calendarGrid.selectedEndDate.getTime());
                    }

                    function isInSelectedRange() {
                        var start = calendarGrid.selectedStartDate.getTime();
                        var end = calendarGrid.selectedEndDate.getTime();
                        var current = dayDelegate.date.getTime();
                        return current > Math.min(start, end) && current < Math.max(start, end);
                    }

                    Text {
                        anchors.centerIn: parent
                        text: dayDelegate.day
                        color: {
                            if (dayDelegate.month !== calendarGrid.month)
                                return "#666666";
                            if (parent.isSelectedDate() || parent.isInSelectedRange() || dayDelegate.today)
                                return "white";
                            return "#cccccc";
                        }
                        font.pixelSize: 12
                        font.bold: dayDelegate.today
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            if (calendarGrid.selectingStartDate) {
                                calendarGrid.selectedStartDate = dayDelegate.date;
                                calendarGrid.selectedEndDate = dayDelegate.date;
                                calendarGrid.selectingStartDate = false;
                            } else {
                                calendarGrid.selectedEndDate = dayDelegate.date;
                                calendarGrid.selectingStartDate = true;
                            }
                        }
                    }
                }
            }

            CustomRangeSlider {
                id: atomicNumberSlider
                labelText: "Atomic Number"
                fromValue: 1
                toValue: 96
                firstValue: 1 
                secondValue: 96
                decimalPlaces: 0
            }

            CustomRangeSlider {
                id: atomicMassSlider
                labelText: "Atomic Mass"
                fromValue: 1
                toValue: 250
                firstValue: 1
                secondValue: 250
                decimalPlaces: 1
            }

            CustomRangeSlider {
                id: q1Slider
                labelText: "Q1 Value"
                fromValue: 1
                toValue: 47
                firstValue: 1
                secondValue: 47
                decimalPlaces: 0
            }

            CustomRangeSlider {
                id: q2Slider
                labelText: "Q2 Value"
                fromValue: 1
                toValue: 56
                firstValue: 1
                secondValue: 56
                decimalPlaces: 0
            }

            CustomRangeSlider {
                id: q3Slider
                labelText: "Q3 Value"
                fromValue: 1
                toValue: 62
                firstValue: 1
                secondValue: 62
                decimalPlaces: 0
            }

            CustomRangeSlider {
                id: maxEnergySlider
                labelText: "Max Energy"
                fromValue: 0
                toValue: 3882
                firstValue: 0
                secondValue: 3882
                decimalPlaces: 0
            }

            CustomRangeSlider {
                id: chargeMassRatioSlider
                labelText: "Q1/M"
                fromValue: 0
                toValue: 3
                firstValue: 0
                secondValue: 3
                decimalPlaces: 0
            }

            CustomRangeSlider {
                id: rigiditySlider
                labelText: "Rigidity"
                fromValue: 0
                toValue: 55
                firstValue: 0
                secondValue: 55
                decimalPlaces: 2
            }

            CheckBox {
                id: showRaisorOption
                text: qsTr("Only RAISOR experiments")

                indicator: Rectangle {
                    implicitWidth: 26
                    implicitHeight: 26
                    x: showRaisorOption.leftPadding
                    y: parent.height / 2 - height / 2
                    radius: 3
                    border.color: showRaisorOption.down ? "#48aaad" : "white"
                    color: "#3e424b"

                    Rectangle {
                        width: 14
                        height: 14
                        x: 6
                        y: 6
                        radius: 2
                        color: showRaisorOption.down ? "#48aaad" : "white"
                        visible: showRaisorOption.checked
                    }
                }

                contentItem: Text {
                    text: showRaisorOption.text
                    font: showRaisorOption.font
                    opacity: enabled ? 1.0 : 0.3
                    color: showRaisorOption.down ? "#48aaad" : "white"
                    verticalAlignment: Text.AlignVCenter
                    leftPadding: showRaisorOption.indicator.width + showRaisorOption.spacing
                }
            }

            Text {
                text: qsTr("Ion Source")
                color: "white"
                font.pixelSize: 14
                font.bold: true
            }

            CustomComboBox {
                id: ionSourceFilter
                Layout.fillWidth: true
                Layout.preferredHeight: 40
                options: [qsTr("All Ion Sources"), qsTr("ECR1"), qsTr("ECR2"), qsTr("ECR3"),]
            }

            Text {
                text: qsTr("Target Material")
                color: "white"
                font.pixelSize: 14
                font.bold: true
            }

            CustomComboBox {
                id: targetFilter
                Layout.fillWidth: true
                Layout.preferredHeight: 40
                options: [
                    qsTr("All Targets"), 
                    qsTr("A2"), 
                    qsTr("A3"), 
                    qsTr("A4"), 
                    qsTr("A5"), 
                    qsTr("A6"), 
                    qsTr("A8"), 
                    qsTr("B4"), 
                    qsTr("B5"), 
                    qsTr("C2"), 
                    qsTr("C4"), 
                    qsTr("P4"),
                ]
            }

            Button {
                id: applyFiltersButton
                text: qsTr("Apply Filters")
                Layout.fillWidth: true
                Layout.preferredHeight: 40
                onClicked: {
                    popup.close();
                    server.get_experiment_data(getFilterQuery());
                }

                background: Rectangle {
                    color: applyFiltersButton.down ? "#5a5a5a" : "#4c4c4c"
                    radius: 4
                }

                contentItem: Text {
                    text: applyFiltersButton.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    anchors.fill: parent
                }
            }
        }
    }
}
