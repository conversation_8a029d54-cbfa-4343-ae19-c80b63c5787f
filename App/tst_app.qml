import QtQuick
import QtTest
import "Components"

Item {
    id: testRoot
    width: 0.6 * Screen.width
    height: 0.8 * Screen.height

    property var initialExperiment: ({
            expNum: "527B",
            startDate: 854517600,
            endDate: 854690400,
            atomicMass: 84,
            atomicNumber: 36,
            ionSrc: "ECR1",
            target: "A3",
            q1: 42,
            q2: 42,
            q3: 42
        })

    // ========== AppBar Tests ==========
    AppBar {
        id: testAppBar
        SignalSpy {
            id: showDrawerSpy
            target: testAppBar
            signalName: "showDrawer"
        }
    }

    TestCase {
        name: "AppBar Tests"
        when: windowShown

        function test_toggle_drawer() {
            compare(showDrawerSpy.count, 0);
            testAppBar.showDrawer();
            compare(showDrawerSpy.count, 1);
        }
    }

    // ========== CustomSearchBar Tests ==========
    CustomSearchBar {
        id: testSearchBar
        placeholderTextString: "Test placeholder"
        
        SignalSpy {
            id: searchTriggeredSpy
            target: testSearchBar
            signalName: "searchTriggered"
        }
        
        SignalSpy {
            id: textClearedSpy
            target: testSearchBar
            signalName: "textCleared"
        }
    }

    TestCase {
        name: "CustomSearchBar Tests"
        when: windowShown

        function init() {
            testSearchBar.text = "";
            searchTriggeredSpy.clear();
            textClearedSpy.clear();
        }

        function test_placeholder_text() {
            compare(testSearchBar.placeholderText, qsTr("Test placeholder"));
        }

        function test_search_triggered_on_enter() {
            testSearchBar.text = "test search";
            searchTriggeredSpy.clear();
            keyPress(Qt.Key_Return);
            compare(searchTriggeredSpy.count, 1);
            compare(searchTriggeredSpy.signalArguments[0][0], "test search");
        }

        function test_text_cleared_signal() {
            testSearchBar.text = "some text";
            textClearedSpy.clear();
            testSearchBar.text = "";
            compare(textClearedSpy.count, 1);
        }

        function test_clear_search_function() {
            textClearedSpy.clear();
            testSearchBar.text = "test";
            testSearchBar.clearSearch();
            compare(testSearchBar.text, "");
            compare(textClearedSpy.count, 1);
        }

        function test_set_search_text_function() {
            searchTriggeredSpy.clear();
            testSearchBar.setSearchText("programmatic search");
            compare(testSearchBar.text, "programmatic search");
            compare(searchTriggeredSpy.count, 1);
            compare(searchTriggeredSpy.signalArguments[0][0], "programmatic search");
        }

        function test_styling_properties() {
            testSearchBar.backgroundColor = "#ff0000";
            testSearchBar.borderColor = "#00ff00";
            testSearchBar.textColor = "#0000ff";
            compare(testSearchBar.backgroundColor, "#ff0000");
            compare(testSearchBar.borderColor, "#00ff00");
            compare(testSearchBar.textColor, "#0000ff");
        }
    }

    // ========== CustomComboBox Tests ==========
    CustomComboBox {
        id: testComboBox
        options: ["Option 1", "Option 2", "Option 3"]
    }

    TestCase {
        name: "CustomComboBox Tests"
        when: windowShown

        function test_options_model() {
            compare(testComboBox.model.length, 3);
            compare(testComboBox.model[0], "Option 1");
            compare(testComboBox.model[1], "Option 2");
            compare(testComboBox.model[2], "Option 3");
        }

        function test_current_selection() {
            testComboBox.currentIndex = 1;
            compare(testComboBox.currentText, "Option 2");
        }

        function test_popup_opens() {
            verify(!testComboBox.popup.visible);
            mouseClick(testComboBox);
            verify(testComboBox.popup.visible);
        }

        function test_label_text_property() {
            testComboBox.labelText = "Test Label";
            compare(testComboBox.labelText, "Test Label");
        }
    }

    // ========== CustomRangeSlider Tests ==========
    CustomRangeSlider {
        id: testRangeSlider
        labelText: "Test Range"
        fromValue: 0
        toValue: 100
        firstValue: 25
        secondValue: 75
        decimalPlaces: 1
    }

    TestCase {
        name: "CustomRangeSlider Tests"
        when: windowShown

        function test_initial_values() {
            compare(testRangeSlider.labelText, "Test Range");
            compare(testRangeSlider.fromValue, 0);
            compare(testRangeSlider.toValue, 100);
            compare(testRangeSlider.firstValue, 25);
            compare(testRangeSlider.secondValue, 75);
        }

        function test_slider_values_exposed() {
            // The actual slider values should be accessible
            verify(testRangeSlider.firstSliderValue >= testRangeSlider.fromValue);
            verify(testRangeSlider.secondSliderValue <= testRangeSlider.toValue);
            verify(testRangeSlider.firstSliderValue <= testRangeSlider.secondSliderValue);
        }

        function test_decimal_places() {
            compare(testRangeSlider.decimalPlaces, 1);
        }

        function test_step_size() {
            testRangeSlider.stepSize = 5;
            compare(testRangeSlider.stepSize, 5);
        }
    }

    // ========== ExperimentViewer Tests ==========
    ExperimentViewer {
        id: testExperimentViewer
        selectedExperiment: testRoot.initialExperiment

        SignalSpy {
            id: selectCommentSpy
            target: testExperimentViewer
            signalName: "selectComment"
        }

        SignalSpy {
            id: selectTimestampSpy
            target: testExperimentViewer
            signalName: "selectTimestamp"
        }
    }

    TestCase {
        name: "ExperimentViewer Tests"
        when: windowShown

        function test_selected_experiment_display() {
            compare(testExperimentViewer.selectedExperiment.expNum, "527B");
            compare(testExperimentViewer.selectedExperiment.q1, 42);
            compare(testExperimentViewer.selectedExperiment.ionSrc, "ECR1");
        }

        function test_experiment_properties() {
            verify(testExperimentViewer.selectedExperiment !== null);
            compare(testExperimentViewer.selectedExperiment.atomicMass, 84);
            compare(testExperimentViewer.selectedExperiment.atomicNumber, 36);
            compare(testExperimentViewer.selectedExperiment.target, "A3");
        }

        function test_signals_exist() {
            verify(selectCommentSpy.target !== null);
            verify(selectTimestampSpy.target !== null);
        }
    }

    // ========== ExperimentList Tests ==========
    ExperimentList {
        id: testExperimentList
        SignalSpy {
            id: selectExperimentSpy
            target: testExperimentList
            signalName: "selectExperiment"
        }
    }

    TestCase {
        name: "ExperimentList Tests"
        when: windowShown

        function test_select_experiment_signal() {
            compare(selectExperimentSpy.count, 0);

            var testExperiment = {
                expNum: "5290-42",
                startDate: 853308000,
                endDate: 853740000,
                atomicMass: 42,
                atomicNumber: 42,
                ionSrc: "NIS",
                target: "A3",
                q1: 42,
                q2: 42,
                q3: 42
            };

            testExperimentList.selectExperiment(testExperiment);
            compare(selectExperimentSpy.count, 1);

            var args = selectExperimentSpy.signalArguments[0];
            verify(args.length > 0);
            compare(args[0].expNum, "5290-42");
            compare(args[0].atomicMass, 42);
            compare(args[0].ionSrc, "NIS");
        }

        function test_filter_menu_function() {
            // Test that the filter menu can be opened
            testExperimentList.openFilterMenu();
            // This should not throw an error
            verify(true);
        }
    }

    // ========== ExperimentListFilterMenu Tests ==========
    ExperimentListFilterMenu {
        id: testFilterMenu
    }

    TestCase {
        name: "ExperimentListFilterMenu Tests"
        when: windowShown

        function test_filter_menu_creation() {
            verify(testFilterMenu !== null);
        }

        function test_get_filter_query_function() {
            // Test that the getFilterQuery function exists and returns a string
            var query = testFilterMenu.getFilterQuery();
            verify(typeof query === "string");
        }

        function test_popup_properties() {
            verify(testFilterMenu.width > 0);
            verify(testFilterMenu.height > 0);
            verify(!testFilterMenu.visible); // Should start closed
        }

        function test_open_close_popup() {
            testFilterMenu.open();
            verify(testFilterMenu.visible);
            testFilterMenu.close();
            verify(!testFilterMenu.visible);
        }
    }

    // ========== CommentEditor Tests ==========
    CommentEditor {
        id: testCommentEditor
        comment: "Test comment"
        timestamp: 1234567890

        SignalSpy {
            id: saveCommentSpy
            target: testCommentEditor
            signalName: "saveComment"
        }
    }

    TestCase {
        name: "CommentEditor Tests"
        when: windowShown

        function test_comment_property() {
            compare(testCommentEditor.comment, "Test comment");
        }

        function test_timestamp_property() {
            compare(testCommentEditor.timestamp, 1234567890);
        }

        function test_save_comment_signal() {
            verify(saveCommentSpy.target !== null);
        }

        function test_comment_editor_creation() {
            verify(testCommentEditor !== null);
        }
    }

    // ========== BeamlinePath Tests ==========
    BeamlinePath {
        id: testBeamlinePath
        selectedExperiment: testRoot.initialExperiment
    }

    TestCase {
        name: "BeamlinePath Tests"
        when: windowShown

        function test_selected_experiment_property() {
            compare(testBeamlinePath.selectedExperiment.expNum, "527B");
        }

        function test_beamline_path_creation() {
            verify(testBeamlinePath !== null);
        }

        function test_beamline_path_dimensions() {
            verify(testBeamlinePath.width >= 0);
            verify(testBeamlinePath.height >= 0);
        }
    }

    // ========== Integration Tests ==========
    TestCase {
        name: "Integration Tests"
        when: windowShown

        function test_experiment_selection_flow() {
            // Test the flow from ExperimentList to ExperimentViewer
            var testExperiment = {
                expNum: "TEST-123",
                startDate: 1000000000,
                endDate: 1000086400,
                atomicMass: 50,
                atomicNumber: 25,
                ionSrc: "ECR2",
                target: "B4",
                q1: 25,
                q2: 25,
                q3: 25
            };

            // Simulate experiment selection
            testExperimentViewer.selectedExperiment = testExperiment;

            // Verify the experiment is properly set
            compare(testExperimentViewer.selectedExperiment.expNum, "TEST-123");
            compare(testExperimentViewer.selectedExperiment.atomicMass, 50);
            compare(testExperimentViewer.selectedExperiment.ionSrc, "ECR2");
        }

        function test_search_bar_integration() {
            // Test search functionality across components
            testSearchBar.setSearchText("integration test");
            compare(testSearchBar.text, "integration test");

            // Clear the search
            testSearchBar.clearSearch();
            compare(testSearchBar.text, "");
        }

        function test_combo_box_integration() {
            // Test combo box selection
            testComboBox.currentIndex = 2;
            compare(testComboBox.currentText, "Option 3");

            // Change options and test again
            testComboBox.options = ["New Option 1", "New Option 2"];
            compare(testComboBox.model.length, 2);
        }

        function test_range_slider_integration() {
            // Test range slider value changes
            var initialFirst = testRangeSlider.firstSliderValue;
            var initialSecond = testRangeSlider.secondSliderValue;

            // Values should be within expected range
            verify(initialFirst >= testRangeSlider.fromValue);
            verify(initialSecond <= testRangeSlider.toValue);
            verify(initialFirst <= initialSecond);
        }
    }
}
