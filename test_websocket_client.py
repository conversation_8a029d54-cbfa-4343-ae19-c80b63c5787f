import pytest
import asyncio
import json
import os
from unittest.mock import Mo<PERSON>, patch, AsyncMock, MagicMock, mock_open
from PySide6.QtCore import QObject

# Import the module to test
from websocket_client import WebsocketClient


class TestWebsocketClient:
    """Test the WebsocketClient class."""
    
    @pytest.fixture
    def websocket_client(self):
        """Create a WebsocketClient instance for testing."""
        return WebsocketClient()
    
    def test_websocket_client_initialization(self, websocket_client):
        """Test WebsocketClient initialization."""
        assert isinstance(websocket_client, QObject)
        assert hasattr(websocket_client, 'commentFetched')
        assert hasattr(websocket_client, 'beamlinePathFetched')
    
    def test_websocket_client_signals_exist(self, websocket_client):
        """Test that WebsocketClient has the required signals."""
        # Check that signals exist and are callable
        assert callable(websocket_client.commentFetched.emit)
        assert callable(websocket_client.beamlinePathFetched.emit)
    
    def test_select_exp_num_sets_attribute(self, websocket_client):
        """Test that select_exp_num sets the selected_exp_num attribute."""
        test_exp_num = "EXP123"
        
        with patch('asyncio.run_coroutine_threadsafe') as mock_run:
            websocket_client.select_exp_num(test_exp_num)
            
            assert websocket_client.selected_exp_num == test_exp_num
    
    def test_select_exp_num_background_execution(self, websocket_client):
        """Test select_exp_num with background execution."""
        test_exp_num = "EXP123"
        
        # Mock background_loop
        with patch('websocket_client.background_loop', Mock()):
            with patch('asyncio.run_coroutine_threadsafe') as mock_run:
                websocket_client.select_exp_num(test_exp_num, run_in_background=True)
                
                # Verify coroutine was scheduled in background
                mock_run.assert_called_once()
    
    def test_select_exp_num_foreground_execution(self, websocket_client):
        """Test select_exp_num with foreground execution."""
        test_exp_num = "EXP123"
        
        with patch('asyncio.create_task') as mock_create_task:
            websocket_client.select_exp_num(test_exp_num, run_in_background=False)
            
            # Verify task was created
            mock_create_task.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_fetch_comment_and_beamline_data_success(self, websocket_client):
        """Test successful websocket data fetching."""
        websocket_client.selected_exp_num = "EXP123"
        
        # Mock websocket messages
        comment_data = {
            "comments": [
                {"timestamp": 1234567890, "comment": "Test comment"}
            ]
        }
        beamline_data = {
            "beamline": [
                {"device": "device1", "value": 123}
            ]
        }
        
        messages = [
            json.dumps(comment_data),
            json.dumps(beamline_data)
        ]
        
        # Mock websocket connection
        mock_websocket = AsyncMock()
        mock_websocket.__aiter__.return_value = iter(messages)
        mock_websocket.__aenter__.return_value = mock_websocket
        mock_websocket.__aexit__.return_value = None
        
        # Mock signals
        websocket_client.commentFetched.emit = Mock()
        websocket_client.beamlinePathFetched.emit = Mock()
        
        with patch('websocket_client.connect', return_value=mock_websocket):
            with patch('builtins.open', mock_open()) as mock_file:
                await websocket_client.fetch_comment_and_beamline_data()
                
                # Verify signals were emitted
                websocket_client.commentFetched.emit.assert_called_once_with(json.dumps(comment_data))
                websocket_client.beamlinePathFetched.emit.assert_called_once_with(json.dumps(beamline_data))
                
                # Verify file was written
                mock_file.assert_called_with('output.txt', 'w')
    
    @pytest.mark.asyncio
    async def test_fetch_comment_and_beamline_data_json_decode_error(self, websocket_client):
        """Test websocket data fetching with JSON decode error."""
        websocket_client.selected_exp_num = "EXP123"
        
        # Mock invalid JSON message
        messages = ["invalid json", '{"valid": "json"}']
        
        mock_websocket = AsyncMock()
        mock_websocket.__aiter__.return_value = iter(messages)
        mock_websocket.__aenter__.return_value = mock_websocket
        mock_websocket.__aexit__.return_value = None
        
        with patch('websocket_client.connect', return_value=mock_websocket):
            with patch('builtins.open', mock_open()):
                with patch('builtins.print') as mock_print:
                    await websocket_client.fetch_comment_and_beamline_data()
                    
                    # Verify error was printed for invalid JSON
                    mock_print.assert_called_with("Received non-JSON message:", "invalid json")
    
    @pytest.mark.asyncio
    async def test_fetch_comment_and_beamline_data_connection_error(self, websocket_client):
        """Test websocket data fetching with connection error."""
        websocket_client.selected_exp_num = "EXP123"
        
        connection_error = Exception("Connection failed")
        
        with patch('websocket_client.connect', side_effect=connection_error):
            with patch('builtins.print') as mock_print:
                await websocket_client.fetch_comment_and_beamline_data()
                
                # Verify error was printed
                mock_print.assert_called_with("WebSocket connection failed:", connection_error)
    
    @pytest.mark.asyncio
    async def test_websocket_url_construction(self, websocket_client):
        """Test that websocket URL is constructed correctly."""
        websocket_client.selected_exp_num = "EXP123"
        
        mock_websocket = AsyncMock()
        mock_websocket.__aiter__.return_value = iter([])
        mock_websocket.__aenter__.return_value = mock_websocket
        mock_websocket.__aexit__.return_value = None
        
        with patch('websocket_client.connect', return_value=mock_websocket) as mock_connect:
            with patch('builtins.open', mock_open()):
                await websocket_client.fetch_comment_and_beamline_data()
                
                # Verify URL construction
                expected_url = "ws://127.0.0.1/ws?expnum=EXP123&has_comments=True"
                mock_connect.assert_called_once_with(expected_url, port=8080, max_size=None)
    
    @pytest.mark.asyncio
    async def test_file_writing(self, websocket_client):
        """Test that data is written to output file."""
        websocket_client.selected_exp_num = "EXP123"
        
        test_data = {"test": "data"}
        messages = [json.dumps(test_data)]
        
        mock_websocket = AsyncMock()
        mock_websocket.__aiter__.return_value = iter(messages)
        mock_websocket.__aenter__.return_value = mock_websocket
        mock_websocket.__aexit__.return_value = None
        
        with patch('websocket_client.connect', return_value=mock_websocket):
            with patch('builtins.open', mock_open()) as mock_file:
                with patch('json.dump') as mock_json_dump:
                    await websocket_client.fetch_comment_and_beamline_data()
                    
                    # Verify file was opened for writing
                    mock_file.assert_called_with('output.txt', 'w')
                    
                    # Verify JSON was dumped to file
                    mock_json_dump.assert_called_with(test_data, mock_file.return_value.__enter__.return_value, indent=2)
    
    @pytest.mark.asyncio
    async def test_comment_data_processing(self, websocket_client):
        """Test processing of comment data specifically."""
        websocket_client.selected_exp_num = "EXP123"
        
        comment_data = {
            "comments": [
                {"timestamp": 1234567890, "comment": "First comment"},
                {"timestamp": 1234567900, "comment": "Second comment"}
            ],
            "other_field": "should be ignored for comment signal"
        }
        
        messages = [json.dumps(comment_data)]
        
        mock_websocket = AsyncMock()
        mock_websocket.__aiter__.return_value = iter(messages)
        mock_websocket.__aenter__.return_value = mock_websocket
        mock_websocket.__aexit__.return_value = None
        
        websocket_client.commentFetched.emit = Mock()
        websocket_client.beamlinePathFetched.emit = Mock()
        
        with patch('websocket_client.connect', return_value=mock_websocket):
            with patch('builtins.open', mock_open()):
                await websocket_client.fetch_comment_and_beamline_data()
                
                # Verify only comment signal was emitted
                websocket_client.commentFetched.emit.assert_called_once_with(json.dumps(comment_data))
                websocket_client.beamlinePathFetched.emit.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_beamline_data_processing(self, websocket_client):
        """Test processing of beamline data specifically."""
        websocket_client.selected_exp_num = "EXP123"
        
        beamline_data = {
            "beamline": [
                {"device": "device1", "value": 123},
                {"device": "device2", "value": 456}
            ],
            "other_field": "should be ignored for beamline signal"
        }
        
        messages = [json.dumps(beamline_data)]
        
        mock_websocket = AsyncMock()
        mock_websocket.__aiter__.return_value = iter(messages)
        mock_websocket.__aenter__.return_value = mock_websocket
        mock_websocket.__aexit__.return_value = None
        
        websocket_client.commentFetched.emit = Mock()
        websocket_client.beamlinePathFetched.emit = Mock()
        
        with patch('websocket_client.connect', return_value=mock_websocket):
            with patch('builtins.open', mock_open()):
                await websocket_client.fetch_comment_and_beamline_data()
                
                # Verify only beamline signal was emitted
                websocket_client.beamlinePathFetched.emit.assert_called_once_with(json.dumps(beamline_data))
                websocket_client.commentFetched.emit.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_mixed_data_processing(self, websocket_client):
        """Test processing of data with both comments and beamline."""
        websocket_client.selected_exp_num = "EXP123"
        
        mixed_data = {
            "comments": [{"timestamp": 1234567890, "comment": "Test comment"}],
            "beamline": [{"device": "device1", "value": 123}]
        }
        
        messages = [json.dumps(mixed_data)]
        
        mock_websocket = AsyncMock()
        mock_websocket.__aiter__.return_value = iter(messages)
        mock_websocket.__aenter__.return_value = mock_websocket
        mock_websocket.__aexit__.return_value = None
        
        websocket_client.commentFetched.emit = Mock()
        websocket_client.beamlinePathFetched.emit = Mock()
        
        with patch('websocket_client.connect', return_value=mock_websocket):
            with patch('builtins.open', mock_open()):
                await websocket_client.fetch_comment_and_beamline_data()
                
                # Verify both signals were emitted
                websocket_client.commentFetched.emit.assert_called_once_with(json.dumps(mixed_data))
                websocket_client.beamlinePathFetched.emit.assert_called_once_with(json.dumps(mixed_data))


class TestWebsocketClientGlobalState:
    """Test global state and threading aspects of websocket_client module."""
    
    def test_background_loop_initialization(self):
        """Test that background loop is initialized."""
        # The background loop should be created when the module is imported
        import websocket_client
        # Note: In actual tests, you might want to mock the threading to avoid real threads
        assert hasattr(websocket_client, 'background_loop')
    
    def test_loop_thread_creation(self):
        """Test that loop thread is created."""
        import websocket_client
        assert hasattr(websocket_client, 'loop_thread')


if __name__ == "__main__":
    pytest.main([__file__])
